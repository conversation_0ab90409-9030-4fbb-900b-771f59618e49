package copier

import (
	"bufio"
	"bytes"
	"compress/gzip"
	"crypto/rand"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"bella/pkg/device"
	"bella/pkg/progress"
)

// Execute runs the appropriate operation based on the config.
func Execute(cfg *Config) error {
	if cfg.DryRun {
		fmt.Printf("Dry run: would perform operation with config: %+v\n", cfg)
		return nil
	}
	switch cfg.Operation {
	case OpCopy:
		if cfg.isRecursive {
			return copyDirectory(cfg)
		}
		return copyFile(cfg)
	case OpWipe:
		return doWipe(cfg)
	case OpVerify:
		return doVerify(cfg)
	default:
		return fmt.Errorf("no operation specified or recognized")
	}
}

func copyFile(cfg *Config) error {
	// Check if we should use multi-stage processing
	if cfg.ShouldUseMultiStage() {
		return copyFileMultiStage(cfg)
	}

	// Standard single-stage copy
	return copyFileSingleStage(cfg)
}

func copyFileMultiStage(cfg *Config) error {
	// We don't create a top-level reporter here; each stage will create its own.
	// This ensures progress bars reset correctly for each phase.

	// Stage 1: Fast kernel copy (raw data)
	rawCfg := *cfg
	rawCfg.Compression = "none"
	rawCfg.Verify = false // Verification is a separate stage

	finalOutput := cfg.Output
	var tempOutput string

	// Use a temporary file if we are verifying or compressing.
	if cfg.Verify || cfg.Compression == "compress" {
		tempOutput = finalOutput + ".tmp.bella"
		defer os.Remove(tempOutput) // Ensure cleanup in case of error
	} else {
		tempOutput = finalOutput
	}
	rawCfg.Output = tempOutput

	// The copy stage will create its own reporter with the "Copying" stage name.
	err := copyFileSingleStage(&rawCfg)
	if err != nil {
		return fmt.Errorf("stage 1 (copy) failed: %w", err)
	}

	// Stage 2: Verification (if requested)
	if cfg.Verify {
		verifyCfg := *cfg
		verifyCfg.Operation = OpVerify
		verifyCfg.Output = tempOutput // Verify the raw (uncompressed) temporary file
		verifyCfg.Compression = "none"

		// The verify stage will create its own reporter.
		err = doVerify(&verifyCfg)
		if err != nil {
			return fmt.Errorf("stage 2 (verification) failed: %w", err)
		}
	}

	// Stage 3: Compression (if requested)
	if cfg.Compression == "compress" {
		// The compress stage will create its own reporter.
		err = compressFile(tempOutput, finalOutput, cfg)
		if err != nil {
			return fmt.Errorf("stage 3 (compression) failed: %w", err)
		}
	} else if tempOutput != finalOutput {
		// If we used a temporary file (e.g., for verification) but didn't compress,
		// we need to rename the temporary file to the final output name.
		if err := os.Rename(tempOutput, finalOutput); err != nil {
			return fmt.Errorf("failed to rename temporary file: %w", err)
		}
	}

	// Final summary message
	if cfg.ProgressChan != nil {
		cfg.ProgressChan <- progress.Info{Summary: "All stages completed successfully!"}
	} else {
		fmt.Println("\nAll stages completed successfully!")
	}

	return nil
}

func copyFileSingleStage(cfg *Config) error {
	in, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open input '%s': %w", cfg.Input, err)
	}
	defer in.Close()

	openFlags := os.O_CREATE | os.O_WRONLY
	if cfg.Append {
		openFlags |= os.O_APPEND
	} else {
		openFlags |= os.O_TRUNC
	}
	out, err := os.OpenFile(cfg.Output, openFlags, 0666)
	if err != nil {
		return fmt.Errorf("failed to open output '%s': %w", cfg.Output, err)
	}
	defer out.Close()

	totalSize, err := device.GetDeviceSize(cfg.Input)
	if err != nil {
		// Only show warning if not in progress mode to avoid interfering with progress display
		if !cfg.Progress || cfg.ProgressChan != nil {
			fmt.Fprintf(os.Stderr, "Warning: could not determine input size: %v\n", err)
		}
		totalSize = 0
	}

	if cfg.Count > 0 {
		countSize := int64(cfg.Count) * int64(cfg.BlockSize)
		if totalSize == 0 || countSize < totalSize {
			totalSize = countSize
		}
	}

	if cfg.ShouldUseCopyOffload() {
		// Kernel offload is so fast, we just show a message.
		if !cfg.Progress || cfg.ProgressChan != nil {
			fmt.Println("Attempting kernel-level copy offload for maximum speed...")
		}
		written, err := device.CopyFileRange(in, out, totalSize)
		if err == nil {
			if !cfg.Progress || cfg.ProgressChan != nil {
				fmt.Printf("Kernel copy successful: %s written.\n", progress.HumanizeBytes(uint64(written)))
			}
			return nil
		}
		if err != syscall.ENOSYS {
			return fmt.Errorf("kernel copy failed: %w", err)
		}
		if !cfg.Progress || cfg.ProgressChan != nil {
			fmt.Println("Kernel offload not supported, falling back to standard copy.")
		}
	}

	if cfg.Skip > 0 {
		if _, err := in.Seek(cfg.Skip*int64(cfg.BlockSize), io.SeekStart); err != nil {
			return fmt.Errorf("failed to skip in input: %w", err)
		}
	}
	if cfg.Seek > 0 && !cfg.Append {
		if _, err := out.Seek(cfg.Seek*int64(cfg.BlockSize), io.SeekStart); err != nil {
			return fmt.Errorf("failed to seek in output: %w", err)
		}
	}

	var reader io.Reader = in
	// Handle decompression
	if cfg.Compression == "decompress" || (cfg.Compression == "auto" && strings.HasSuffix(cfg.Input, ".gz")) {
		if cfg.CompressionType == "gzip" || (cfg.Compression == "auto" && strings.HasSuffix(cfg.Input, ".gz")) {
			gr, err := gzip.NewReader(reader)
			if err != nil {
				return fmt.Errorf("failed to create gzip reader: %w", err)
			}
			defer gr.Close()
			reader = gr
		}
	}

	var writer io.Writer = out
	// Handle compression
	if cfg.Compression == "compress" {
		if cfg.CompressionType == "gzip" {
			gw, err := gzip.NewWriterLevel(writer, gzip.DefaultCompression)
			if err != nil {
				return fmt.Errorf("failed to create gzip writer: %w", err)
			}
			defer gw.Close()
			writer = gw
		}
	}

	bufReader := bufio.NewReaderSize(reader, cfg.BlockSize)
	return copyLoop(bufReader, writer, out, cfg, totalSize)
}

func compressFile(inputPath, outputPath string, cfg *Config) error {
	in, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input for compression: %w", err)
	}
	defer in.Close()

	out, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create compressed output: %w", err)
	}
	defer out.Close()

	// Get file size for progress reporting
	info, err := in.Stat()
	if err != nil {
		return fmt.Errorf("failed to get file info for compression: %w", err)
	}
	totalSize := info.Size()

	var writer io.Writer = out
	if cfg.CompressionType == "gzip" {
		gw, err := gzip.NewWriterLevel(writer, gzip.DefaultCompression)
		if err != nil {
			return fmt.Errorf("failed to create gzip writer: %w", err)
		}
		defer gw.Close()
		writer = gw
	}

	// Set up progress reporting
	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("compress", cfg.ProgressChan)
		reporter.Update(0, totalSize)
	}

	// Copy with progress
	buf := make([]byte, cfg.BlockSize)
	var written int64
	for {
		n, err := in.Read(buf)
		if n > 0 {
			if _, wErr := writer.Write(buf[:n]); wErr != nil {
				return fmt.Errorf("compression write error: %w", wErr)
			}
			written += int64(n)
			if cfg.Progress {
				reporter.Update(written, totalSize)
			}
		}
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("compression read error: %w", err)
		}
	}

	if cfg.Progress {
		reporter.Finish(written)
	}

	return nil
}

func copyLoop(r io.Reader, w io.Writer, outFile *os.File, cfg *Config, totalSize int64) error {
	buf := make([]byte, cfg.BlockSize)
	var written int64 = 0
	blocksCopied := 0
	var reporter *progress.Reporter

	if cfg.Progress {
		stage := "Copying"
		if cfg.isRecursive {
			stage = "Copying Directory"
		}
		reporter = progress.NewReporter(stage, cfg.ProgressChan)
		reporter.Update(0, totalSize)
	}

	for {
		if cfg.Count > 0 && blocksCopied >= cfg.Count {
			break
		}

		n, err := r.Read(buf)
		if n > 0 {
			if cfg.Sparse && isAllZeros(buf[:n]) {
				if _, seekErr := outFile.Seek(int64(n), io.SeekCurrent); seekErr != nil {
					if _, wErr := w.Write(buf[:n]); wErr != nil {
						return fmt.Errorf("sparse seek and fallback write failed: %w", wErr)
					}
				}
			} else {
				if _, wErr := w.Write(buf[:n]); wErr != nil {
					return fmt.Errorf("write error: %w", wErr)
				}
			}
			written += int64(n)
			blocksCopied++
			if cfg.Progress {
				reporter.Update(written, totalSize)
			}
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			if cfg.SkipBadSectors {
				// Only show warning if not in CLI progress mode to avoid interfering with progress display
				if !cfg.Progress || cfg.ProgressChan != nil {
					fmt.Fprintf(os.Stderr, "\nRead error at offset %d, skipping: %v\n", written, err)
				}
				zeroBuf := make([]byte, cfg.BlockSize)
				if cfg.Sparse {
					if _, seekErr := outFile.Seek(int64(cfg.BlockSize), io.SeekCurrent); seekErr != nil {
						if _, wErr := w.Write(zeroBuf); wErr != nil {
							return fmt.Errorf("error writing zero block for bad sector: %w", wErr)
						}
					}
				} else {
					if _, wErr := w.Write(zeroBuf); wErr != nil {
						return fmt.Errorf("error writing zero block for bad sector: %w", wErr)
					}
				}
				written += int64(cfg.BlockSize)
				continue
			}
			return fmt.Errorf("read error: %w", err)
		}
	}

	if f, ok := w.(io.Closer); ok {
		f.Close()
	}
	if cfg.Progress {
		reporter.Finish(written)
	}

	// Don't run verification here if we're in multi-stage mode
	// (it will be handled by the multi-stage function)
	if cfg.Verify && !cfg.ShouldUseMultiStage() {
		if !cfg.Progress || cfg.ProgressChan != nil {
			fmt.Println("Starting verification...")
		}
		verifyCfg := *cfg
		verifyCfg.Operation = OpVerify
		if err := doVerify(&verifyCfg); err != nil {
			return err
		}
	}

	return nil
}

func copyDirectory(cfg *Config) error {
	// Only show directory copy message if not in CLI progress mode
	if !cfg.Progress || cfg.ProgressChan != nil {
		fmt.Printf("Recursively copying directory %s to %s\n", cfg.Input, cfg.Output)
	}
	return filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err != nil {
			return err
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			return err
		}
		destPath := filepath.Join(cfg.Output, relPath)
		if d.IsDir() {
			info, statErr := os.Stat(path)
			if statErr != nil {
				return statErr
			}
			return os.MkdirAll(destPath, info.Mode())
		}
		fileCfg := *cfg
		fileCfg.Input = path
		fileCfg.Output = destPath
		fileCfg.isRecursive = false
		copyErr := copyFile(&fileCfg)
		if copyErr != nil {
			return fmt.Errorf("failed to copy file %s: %w", path, copyErr)
		}
		if cfg.PreserveAttributes {
			info, statErr := os.Stat(path)
			if statErr != nil {
				return statErr
			}
			os.Chmod(destPath, info.Mode())
			os.Chtimes(destPath, time.Now(), info.ModTime())
		}
		return nil
	})
}

func doWipe(cfg *Config) error {
	out, err := os.OpenFile(cfg.Output, os.O_WRONLY, 0)
	if err != nil {
		return fmt.Errorf("failed to open output for wiping: %w", err)
	}
	defer out.Close()
	totalSize, err := device.GetDeviceSize(cfg.Output)
	if err != nil {
		// Only show warning if not in CLI progress mode to avoid interfering with progress display
		if !cfg.Progress || cfg.ProgressChan != nil {
			fmt.Fprintf(os.Stderr, "Warning: could not determine wipe size: %v\n", err)
		}
		totalSize = 0
	}
	buf := make([]byte, cfg.BlockSize)
	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("wipe", cfg.ProgressChan)
		reporter.Update(0, totalSize)
	}

	for pass := 1; pass <= cfg.WipePasses; pass++ {
		// Only show pass info if not in CLI progress mode to avoid interfering with progress display
		if !cfg.Progress || cfg.ProgressChan != nil {
			fmt.Fprintf(os.Stderr, "Starting pass %d of %d...\n", pass, cfg.WipePasses)
		}
		if _, err := out.Seek(0, io.SeekStart); err != nil {
			return fmt.Errorf("failed to seek to start for wipe pass %d: %w", pass, err)
		}
		var written int64 = 0
		for {
			if totalSize > 0 && written >= totalSize {
				break
			}
			if cfg.WipeMode == "random" {
				if _, err := rand.Read(buf); err != nil {
					return fmt.Errorf("failed to generate random data: %w", err)
				}
			} else {
				for i := range buf {
					buf[i] = 0
				}
			}
			n, err := out.Write(buf)
			if n > 0 {
				written += int64(n)
				if cfg.Progress {
					reporter.Update(written, totalSize)
				}
			}
			if err != nil {
				if written >= totalSize && totalSize > 0 {
					break
				}
				return fmt.Errorf("wipe write error: %w", err)
			}
		}
	}
	if cfg.Progress {
		reporter.Finish(0)
	} //Bytes written is per-pass, so pass 0 for final summary.
	return nil
}

func doVerify(cfg *Config) error {
	// Open source file for reading
	src, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open source '%s' for verification: %w", cfg.Input, err)
	}
	defer src.Close()

	// Check if destination exists before opening
	if _, err := os.Stat(cfg.Output); os.IsNotExist(err) {
		return fmt.Errorf("verification failed: destination file '%s' does not exist", cfg.Output)
	}

	// Open destination file for reading
	dst, err := os.Open(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to open destination '%s' for verification: %w", cfg.Output, err)
	}
	defer dst.Close()

	// Get file sizes
	srcSize, err := device.GetDeviceSize(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to get source size for verification: %w", err)
	}

	dstSize, err := device.GetDeviceSize(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to get destination size for verification: %w", err)
	}

	// Check if sizes match
	if srcSize != dstSize {
		return fmt.Errorf("verification failed: file sizes differ (source: %d, destination: %d)",
			srcSize, dstSize)
	}

	totalSize := srcSize

	// Set up progress reporting
	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("Verifying", cfg.ProgressChan)
		reporter.Update(0, totalSize)
	}

	// Compare files block by block
	srcBuf := make([]byte, cfg.BlockSize)
	dstBuf := make([]byte, cfg.BlockSize)
	var verified int64

	for {
		// Read from source
		srcN, srcErr := src.Read(srcBuf)
		if srcErr != nil && srcErr != io.EOF {
			return fmt.Errorf("error reading source during verification: %w", srcErr)
		}

		// Read from destination
		dstN, dstErr := dst.Read(dstBuf)
		if dstErr != nil && dstErr != io.EOF {
			return fmt.Errorf("error reading destination during verification: %w", dstErr)
		}

		// Check if read sizes match
		if srcN != dstN {
			return fmt.Errorf("verification failed: read size mismatch at offset %d", verified)
		}

		// If we've reached the end of both files
		if srcN == 0 {
			break
		}

		// Compare the data
		if !bytes.Equal(srcBuf[:srcN], dstBuf[:dstN]) {
			return fmt.Errorf("verification failed: data mismatch at offset %d", verified)
		}

		verified += int64(srcN)

		// Update progress
		if cfg.Progress {
			reporter.Update(verified, totalSize)
		}

		// Check for EOF
		if srcErr == io.EOF || dstErr == io.EOF {
			break
		}
	}

	// Finalize progress
	if cfg.Progress {
		reporter.Finish(verified)
	}

	// Send a separate summary message for UI mode if needed
	if cfg.ProgressChan != nil {
		summary := fmt.Sprintf("Verification successful: %s verified", progress.HumanizeBytes(uint64(verified)))
		// This allows the UI to show a final confirmation screen.
		cfg.ProgressChan <- progress.Info{Summary: summary}
	} else {
		// For CLI, the Finish call already printed the summary.
		// We can add a simple confirmation.
		fmt.Fprintf(os.Stderr, " -> Verification successful: %s verified.\n", progress.HumanizeBytes(uint64(verified)))
	}

	return nil
}

func isAllZeros(data []byte) bool {
	for _, b := range data {
		if b != 0 {
			return false
		}
	}
	return true
}
