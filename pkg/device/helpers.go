package device

import (
	"fmt"
	"os"
	"strings"
)

// SuggestBlockSize provides a sensible default block size based on device type.
func SuggestBlockSize(path string) (int, error) {
	// If it's not a device, it's a regular file. 4M is a good general default.
	if !strings.HasPrefix(path, "/dev/") {
		return 4 * 1024 * 1024, nil
	}
	
	// Extract device name (e.g., sda from /dev/sda)
	parts := strings.Split(path, "/")
	devName := parts[len(parts)-1]

	// Check if it's a rotational disk (HDD)
	rotationalPath := fmt.Sprintf("/sys/block/%s/queue/rotational", devName)
	data, err := os.ReadFile(rotationalPath)
	if err != nil {
		// If we can't determine, fall back to a safe default.
		return 1 * 1024 * 1024, fmt.Errorf("could not read device properties: %w", err)
	}

	if strings.TrimSpace(string(data)) == "0" {
		// Not rotational (SSD, NVMe) -> Use a large block size for high performance.
		return 4 * 1024 * 1024, nil
	} else {
		// Rotational (HDD) -> Use a medium block size.
		return 1 * 1024 * 1024, nil
	}
}
