package ui

import (
	"fmt"
	"os"
	"strconv"
	"strings"

	"bella/pkg/copier"
	"bella/pkg/device"
	"bella/pkg/progress"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

// App is the main TUI application structure.
type App struct {
	app   *tview.Application
	pages *tview.Pages
}

// NewApp creates and initializes the TUI application.
func NewApp() *App {
	return &App{
		app:   tview.NewApplication(),
		pages: tview.NewPages(),
	}
}

// Run starts the application's main loop.
func (a *App) Run() error {
	mainMenu := a.createMainMenu()

	// Use a Frame for robust headers/footers.
	frame := tview.NewFrame(mainMenu).
		SetBorders(0, 0, 1, 1, 0, 0)

	// Add a header and a multi-part footer.
	frame.AddText("Bella - Advanced Data Copier", true, tview.AlignCenter, tcell.ColorAqua)

	sudoStatus := "[green]Running as root."
	if os.Getuid() != 0 {
		sudoStatus = "[yellow]Running as user. Restart with 'sudo' for device access."
	}
	frame.AddText(sudoStatus, false, tview.AlignLeft, tcell.ColorDefault)
	frame.AddText("Use arrows, Enter to select, Esc to go back", false, tview.AlignRight, tcell.ColorAqua)

	a.pages.AddPage("main", frame, true, true)
	a.app.SetRoot(a.pages, true).SetFocus(mainMenu)

	return a.app.Run()
}

// goBack is a helper to return to the main menu.
func (a *App) goBack(pageToHide string) {
	a.pages.RemovePage(pageToHide)
	a.pages.SwitchToPage("main")
	// Clear and force redraw to ensure clean screen
	a.app.Sync()
	a.app.ForceDraw()
}

// switchToPage is a helper to cleanly switch between pages
func (a *App) switchToPage(pageName string) {
	a.pages.SwitchToPage(pageName)
	a.app.ForceDraw()
}

// createMainMenu builds the main navigation list.
func (a *App) createMainMenu() *tview.List {
	return tview.NewList().
		SetWrapAround(false).
		AddItem("Copy", "Copy files, directories, or devices", 'c', a.showCopyForm).
		AddItem("Verify", "Verify files by comparing source and destination", 'v', a.showVerifyForm).
		AddItem("Wipe", "Securely wipe a device or file", 'w', a.showWipeForm).
		AddItem("List Devices", "Show available storage devices (display only)", 'l', a.showDeviceList).
		AddItem("Quit", "Exit Bella", 'q', func() { a.app.Stop() })
}

// showCopyForm displays the form for copy operations.
func (a *App) showCopyForm() {
	pageName := "copyForm"
	cfg := copier.NewConfig()

	form := tview.NewForm().
		AddInputField("Input", "", 40, nil, nil).
		AddInputField("Output", "", 40, nil, nil).
		AddInputField("Block Size", "auto", 10, nil, nil).
		AddInputField("Threads", "1", 5, nil, nil).
		AddInputField("Count", "-1", 10, nil, nil).
		AddInputField("Skip", "0", 10, nil, nil).
		AddInputField("Seek", "0", 10, nil, nil).
		AddDropDown("Compression", []string{"none", "compress", "decompress", "auto"}, 0, nil).
		AddDropDown("Compression Type", []string{"gzip"}, 0, nil).
		AddInputField("Checksum", "", 15, nil, nil).
		AddCheckbox("Sparse Copy", false, nil).
		AddCheckbox("Skip Bad Sectors", false, nil).
		AddCheckbox("Verify After Copy", false, nil).
		AddCheckbox("Append Mode", false, nil).
		AddCheckbox("Disable Kernel Copy Offload", false, nil).
		AddCheckbox("Preserve Attributes", false, nil).
		AddCheckbox("Dry Run", false, nil)

	form.AddButton("Browse Input", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})
	form.AddButton("Browse Output", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(1).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Start Copy", func() {
		cfg.Operation = copier.OpCopy
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()

		bsText := form.GetFormItem(2).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Input)
			if err != nil {
				a.showError(fmt.Sprintf("Auto block size failed: %v", err))
				return
			}
			cfg.BlockSize = suggestedBS
			fmt.Printf("Auto block size selected: Using %s for %s\n", progress.HumanizeBytes(uint64(suggestedBS)), cfg.Input)
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showError(fmt.Sprintf("Invalid Block Size: %v", err))
				return
			}
			cfg.BlockSize = bs
		}

		threads, _ := strconv.Atoi(form.GetFormItem(3).(*tview.InputField).GetText())
		if threads < 1 {
			threads = 1
		}
		cfg.Threads = threads

		count, _ := strconv.Atoi(form.GetFormItem(4).(*tview.InputField).GetText())
		cfg.Count = count
		skip, _ := strconv.ParseInt(form.GetFormItem(5).(*tview.InputField).GetText(), 10, 64)
		cfg.Skip = skip
		seek, _ := strconv.ParseInt(form.GetFormItem(6).(*tview.InputField).GetText(), 10, 64)
		cfg.Seek = seek

		_, cfg.Compression = form.GetFormItem(7).(*tview.DropDown).GetCurrentOption()
		_, cfg.CompressionType = form.GetFormItem(8).(*tview.DropDown).GetCurrentOption()
		cfg.Checksum = form.GetFormItem(9).(*tview.InputField).GetText()

		cfg.Sparse = form.GetFormItem(10).(*tview.Checkbox).IsChecked()
		cfg.SkipBadSectors = form.GetFormItem(11).(*tview.Checkbox).IsChecked()
		cfg.Verify = form.GetFormItem(12).(*tview.Checkbox).IsChecked()
		cfg.Append = form.GetFormItem(13).(*tview.Checkbox).IsChecked()
		cfg.UseCopyOffload = !form.GetFormItem(14).(*tview.Checkbox).IsChecked()
		cfg.PreserveAttributes = form.GetFormItem(15).(*tview.Checkbox).IsChecked()
		cfg.DryRun = form.GetFormItem(16).(*tview.Checkbox).IsChecked()

		a.runOperation(cfg)
	})
	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Copy Operation")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showVerifyForm displays the form for verify operations.
func (a *App) showVerifyForm() {
	pageName := "verifyForm"
	cfg := copier.NewConfig()

	form := tview.NewForm().
		AddInputField("Source", "", 40, nil, nil).
		AddInputField("Target", "", 40, nil, nil).
		AddInputField("Block Size", "auto", 10, nil, nil)

	form.AddButton("Browse Source", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})
	form.AddButton("Browse Target", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(1).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Start Verify", func() {
		cfg.Operation = copier.OpVerify
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()

		bsText := form.GetFormItem(2).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Input)
			if err != nil {
				a.showError(fmt.Sprintf("Auto block size failed: %v", err))
				return
			}
			cfg.BlockSize = suggestedBS
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showError(fmt.Sprintf("Invalid Block Size: %v", err))
				return
			}
			cfg.BlockSize = bs
		}

		a.runOperation(cfg)
	})

	form.AddButton("Back", func() { a.goBack(pageName) })
	form.SetBorder(true).SetTitle(" Verify Files ")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showDeviceSelector displays a list of devices and calls onSelect with the chosen path.
func (a *App) showDeviceSelector(returnToPage string, onSelect func(path string)) {
	pageName := "deviceSelector"
	list := tview.NewList().SetWrapAround(false)
	list.SetBorder(true).SetTitle("Select a Device")

	devices, err := device.DetectDevices()
	if err != nil {
		a.showError(fmt.Sprintf("Could not detect devices: %v", err))
		return
	}

	if len(devices) == 0 {
		list.AddItem("No devices found", "", 0, nil)
	}

	for i, d := range devices {
		devicePath := d.Path // Capture the current device path
		sizeStr := progress.HumanizeBytes(uint64(d.Size))
		secondary := fmt.Sprintf("Size: %s, Model: %s", sizeStr, d.Model)

		// Use a closure to properly capture the device path
		func(path string) {
			list.AddItem(d.Path, secondary, rune('0'+i), func() {
				a.pages.RemovePage(pageName)
				onSelect(path)
			})
		}(devicePath)
	}

	list.AddItem("Cancel", "Return to previous screen", 'c', func() {
		a.pages.RemovePage(pageName)
		a.switchToPage(returnToPage)
	})

	list.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.pages.RemovePage(pageName)
			a.switchToPage(returnToPage)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, list, true, true)
}

// runOperation launches the progress screen and the backend logic.
func (a *App) runOperation(cfg *copier.Config) {
	progressChan := make(chan progress.Info, 100)
	cfg.ProgressChan = progressChan
	cfg.Progress = true

	// Create a proper modal that overlays the current page
	a.showProgressModal(progressChan)

	go func() {
		err := copier.Execute(cfg)
		if err != nil {
			defer func() { recover() }() // Safely ignore panic on closed channel
			progressChan <- progress.Info{Summary: fmt.Sprintf("[red]ERROR: %v", err)}
		}
	}()
}

// showProgressModal displays a robust, centered progress dialog.
func (a *App) showProgressModal(progressChan <-chan progress.Info) {
	pageName := "progressModal"

	// Create a semi-transparent background that fills the screen
	bg := tview.NewBox().
		SetBackgroundColor(tcell.NewRGBColor(30, 30, 30)).
		SetBorder(false)

	// Create and store the progress text view with explicit type
	textView := tview.NewTextView()
	textView.SetDynamicColors(true).
		SetRegions(true).
		SetWordWrap(true).
		SetText("Initializing...").
		SetBorder(true).
		SetTitle("Progress").
		SetTitleAlign(tview.AlignCenter)

	// Create a centered flex container for the progress dialog
	centerFlex := tview.NewFlex().
		AddItem(nil, 0, 1, false).
		AddItem(tview.NewFlex().SetDirection(tview.FlexRow).
			AddItem(nil, 0, 1, false).
			AddItem(textView, 0, 1, true).
			AddItem(nil, 0, 1, false),
			0, 1, false).
		AddItem(nil, 0, 1, false)

	// Combine background and progress dialog
	mainFlex := tview.NewFlex().
		SetDirection(tview.FlexRow).
		AddItem(bg, 0, 1, false).
		AddItem(centerFlex, 0, 1, true)

	a.pages.AddPage(pageName, mainFlex, true, true)
	a.app.SetFocus(textView) // Focus the text view
	a.app.ForceDraw()

	// Use the already properly typed textView reference
	progressTextView := textView

	// Handle progress updates in a separate goroutine
	go func() {
		defer func() {
			if r := recover(); r != nil {
				// Panics are expected if the channel closes while the UI is shutting down.
			}
		}()

		for p := range progressChan {
			a.app.QueueUpdateDraw(func() {
				// Check if the progress page is still the front page.
				if front, _ := a.pages.GetFrontPage(); front != pageName {
					return // Stop updates if the user has navigated away.
				}

				if p.Summary != "" {
					// Operation finished - update progress view with final stats
					finalText := formatProgress(p) + "\n\n[green]Operation completed successfully!\n\n[white]Press any key to return"
					progressTextView.SetText(finalText)

					// Change input handler to allow any key to return
					progressTextView.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
						a.goBack(pageName)
						return nil
					})
				} else {
					// Update the progress text.
					progressTextView.SetText(formatProgress(p))
				}
			})
		}
	}()
}

// showWipeForm displays the form for wipe operations.
func (a *App) showWipeForm() {
	pageName := "wipeForm"
	cfg := copier.NewConfig()

	form := tview.NewForm().
		AddInputField("Target Device", "", 40, nil, nil).
		AddDropDown("Mode", []string{"zero", "random"}, 1, nil). // Default to random (index 1)
		AddInputField("Passes", "1", 3, nil, nil).
		AddInputField("Block Size", "4M", 10, nil, nil)

	form.AddButton("Browse Target", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			// Set block size to auto when browse is used
			form.GetFormItem(3).(*tview.InputField).SetText("auto")
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Start Wipe", func() {
		cfg.Operation = copier.OpWipe
		cfg.Output = form.GetFormItem(0).(*tview.InputField).GetText()
		_, cfg.WipeMode = form.GetFormItem(1).(*tview.DropDown).GetCurrentOption()
		passes, _ := strconv.Atoi(form.GetFormItem(2).(*tview.InputField).GetText())
		if passes < 1 {
			passes = 1
		}
		cfg.WipePasses = passes

		bsText := form.GetFormItem(3).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Output)
			if err != nil {
				a.showError(fmt.Sprintf("Auto block size failed: %v", err))
				return
			}
			cfg.BlockSize = suggestedBS
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showError(fmt.Sprintf("Invalid Block Size: %v", err))
				return
			}
			cfg.BlockSize = bs
		}

		a.showConfirmation(fmt.Sprintf("This will IRREVERSIBLY DESTROY ALL DATA on %s.\nAre you sure?", cfg.Output), func() {
			a.runOperation(cfg)
		})
	})

	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Wipe Operation")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showDeviceList displays a list of detected storage devices with detailed info on selection.
func (a *App) showDeviceList() {
	pageName := "deviceList"
	list := tview.NewList().SetWrapAround(false)
	list.SetBorder(true).SetTitle("Available Devices - Press Enter for details")

	devices, err := device.DetectDevices()
	if err != nil {
		a.showError(fmt.Sprintf("Could not detect devices: %v", err))
		return
	}

	if len(devices) == 0 {
		list.AddItem("No devices found", "", 0, nil)
	}

	for i, d := range devices {
		deviceInfo := d // Capture device info
		sizeStr := progress.HumanizeBytes(uint64(d.Size))
		secondary := fmt.Sprintf("Size: %s, Model: %s", sizeStr, d.Model)

		list.AddItem(d.Path, secondary, rune('0'+i), func() {
			a.showDeviceDetails(deviceInfo)
		})
	}

	list.AddItem("Back", "Return to main menu", 'b', func() { a.goBack(pageName) })

	list.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, list, true, true)
}

// showDeviceDetails displays detailed information about a device and its partitions
func (a *App) showDeviceDetails(deviceInfo device.Info) {
	pageName := "deviceDetails"

	// Create a flex container for the layout
	mainFlex := tview.NewFlex().SetDirection(tview.FlexRow)
	mainFlex.SetBorder(true).SetTitle(fmt.Sprintf("Device Details: %s", deviceInfo.Path))

	// Get detailed device information
	detailInfo, err := device.GetDetailedDeviceInfo(deviceInfo.Path)
	if err != nil {
		a.showError(fmt.Sprintf("Could not get device details: %v", err))
		return
	}

	// Create device info text view
	deviceInfoText := tview.NewTextView().SetDynamicColors(true)
	deviceInfoText.SetBorder(true).SetTitle("Device Information")

	infoText := fmt.Sprintf("[yellow]Device:[white] %s\n", deviceInfo.Path)
	infoText += fmt.Sprintf("[yellow]Name:[white] %s\n", deviceInfo.Name)
	infoText += fmt.Sprintf("[yellow]Size:[white] %s\n", progress.HumanizeBytes(uint64(deviceInfo.Size)))

	for key, value := range detailInfo {
		if key != "Size" { // Skip size as we already show it in a nicer format
			infoText += fmt.Sprintf("[yellow]%s:[white] %s\n", key, value)
		}
	}

	deviceInfoText.SetText(infoText)

	// Get partitions for this device
	partitions, err := device.GetPartitions(deviceInfo.Name)
	if err != nil {
		a.showError(fmt.Sprintf("Could not get partitions: %v", err))
		return
	}

	// Create partitions list
	partitionsList := tview.NewList().SetWrapAround(false)
	partitionsList.SetBorder(true).SetTitle("Partitions")

	if len(partitions) == 0 {
		partitionsList.AddItem("No partitions found", "", 0, nil)
	} else {
		for i, p := range partitions {
			sizeStr := progress.HumanizeBytes(uint64(p.Size))
			secondary := fmt.Sprintf("Size: %s", sizeStr)
			partitionsList.AddItem(p.Path, secondary, rune('0'+i), nil)
		}
	}

	// Add back button to partitions list
	partitionsList.AddItem("Back", "Return to device list", 'b', func() {
		a.pages.RemovePage(pageName)
		a.switchToPage("deviceList")
	})

	// Layout: device info on top, partitions on bottom
	mainFlex.AddItem(deviceInfoText, 0, 1, false).
		AddItem(partitionsList, 0, 1, true)

	// Handle escape key
	mainFlex.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.pages.RemovePage(pageName)
			a.switchToPage("deviceList")
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, mainFlex, true, true)
}

// Helper functions
func (a *App) showError(message string) {
	pageName := "errorModal"
	modal := tview.NewModal().
		SetText(message).
		AddButtons([]string{"OK"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			a.pages.RemovePage(pageName)
			// Force a redraw to ensure clean screen
			a.app.ForceDraw()
		})
	a.pages.AddPage(pageName, modal, true, true)
}

func (a *App) showConfirmation(message string, onConfirm func()) {
	pageName := "confirmModal"
	modal := tview.NewModal().
		SetText(message).
		AddButtons([]string{"Yes", "No"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			a.pages.RemovePage(pageName)
			// Force a redraw to ensure clean screen
			a.app.ForceDraw()
			if buttonLabel == "Yes" {
				onConfirm()
			}
		})
	a.pages.AddPage(pageName, modal, true, true)
}

// formatProgress creates a formatted string for the progress view.
func formatProgress(p progress.Info) string {
	barWidth := 50
	filled := int(p.PercentDone * float64(barWidth) / 100.0)
	if filled < 0 {
		filled = 0
	}
	if filled > barWidth {
		filled = barWidth
	}

	// Create a visually appealing bar.
	bar := fmt.Sprintf("[green]%s[grey]%s[white]", strings.Repeat("█", filled), strings.Repeat("░", barWidth-filled))

	// Format total size, handling unknown totals gracefully.
	totalStr := progress.HumanizeBytes(uint64(p.BytesTotal))
	if p.BytesTotal <= 0 {
		totalStr = "???"
	}

	// Determine the current stage of the operation.
	stage := "Processing"
	if p.Stage != "" {
		stage = p.Stage
	}

	// Format ETA, handle empty ETA gracefully
	etaStr := p.ETA
	if etaStr == "" {
		etaStr = "Calculating..."
	}

	// Build the final formatted string with better spacing and alignment.
	return fmt.Sprintf(
		`[yellow]Stage:[white] %s

%s
[white][b]%.1f%%[white]

[cyan]Data:[white]   %s / %s
[cyan]Speed:[white]  %.2f MB/s
[cyan]ETA:[white]    %s`,
		stage,
		bar,
		p.PercentDone,
		progress.HumanizeBytes(uint64(p.BytesDone)),
		totalStr,
		p.AvgSpeed,
		etaStr,
	)
}

func parseBlockSize(s string) (int, error) {
	s = strings.ToUpper(strings.TrimSpace(s))
	mult := 1
	suffix := ""
	if strings.HasSuffix(s, "K") || strings.HasSuffix(s, "M") || strings.HasSuffix(s, "G") {
		suffix = s[len(s)-1:]
		s = s[:len(s)-1]
	}
	val, err := strconv.Atoi(s)
	if err != nil {
		return 0, err
	}
	switch suffix {
	case "K":
		mult = 1024
	case "M":
		mult = 1024 * 1024
	case "G":
		mult = 1024 * 1024 * 1024
	}
	return val * mult, nil
}
